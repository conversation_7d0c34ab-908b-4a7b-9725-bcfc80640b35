# Ethereum Block Scheduler Configuration Example
# Copy this file to .env and update with your actual values

# =============================================================================
# ETHEREUM CONFIGURATION
# =============================================================================

# Ethereum RPC URL (required)
# Get from Infura, Alchemy, or other providers
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID

# Ethereum WebSocket URL (required for real-time mode)
# Get from Infura, Alchemy, or other providers
ETHEREUM_WS_URL=wss://mainnet.infura.io/ws/v3/YOUR_PROJECT_ID

# Starting block number (use 'latest' for real-time processing)
START_BLOCK_NUMBER=latest

# Request timeout for Ethereum API calls
ETHEREUM_REQUEST_TIMEOUT=90s

# Rate limiting between API requests (important for free tiers)
ETHEREUM_RATE_LIMIT=500ms

# Skip transaction receipts to speed up processing
ETHEREUM_SKIP_RECEIPTS=false

# =============================================================================
# MONGODB CONFIGURATION
# =============================================================================

# MongoDB connection URI
MONGO_URI=mongodb://localhost:27017/ethereum_raw_data

# MongoDB database name
MONGO_DATABASE=ethereum_raw_data

# MongoDB connection timeout
MONGO_CONNECT_TIMEOUT=10s

# MongoDB connection pool size
MONGO_MAX_POOL_SIZE=100

# =============================================================================
# SCHEDULER CONFIGURATION
# =============================================================================

# Scheduler mode: polling, realtime, hybrid (recommended: hybrid)
SCHEDULER_MODE=hybrid

# Enable WebSocket real-time block listening
SCHEDULER_ENABLE_REALTIME=true

# Enable polling fallback
SCHEDULER_ENABLE_POLLING=true

# Polling interval (how often to check for new blocks)
SCHEDULER_POLLING_INTERVAL=3s

# Fallback timeout (switch to polling if no WebSocket blocks for this duration)
SCHEDULER_FALLBACK_TIMEOUT=30s

# WebSocket reconnection attempts
SCHEDULER_RECONNECT_ATTEMPTS=5

# Delay between reconnection attempts
SCHEDULER_RECONNECT_DELAY=5s

# =============================================================================
# CRAWLER CONFIGURATION
# =============================================================================

# Batch size for processing blocks (1 for real-time, higher for batch)
BATCH_SIZE=1

# Number of concurrent workers
CONCURRENT_WORKERS=5

# Retry attempts for failed operations
RETRY_ATTEMPTS=3

# Delay between retry attempts
RETRY_DELAY=2s

# Batch upsert configuration (recommended for better performance)
CRAWLER_USE_UPSERT=true
CRAWLER_UPSERT_FALLBACK=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application port (for health checks)
APP_PORT=8080

# Environment (development, production)
APP_ENV=development

# Log level (debug, info, warn, error)
LOG_LEVEL=info

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable metrics collection
METRICS_ENABLED=true

# Health check interval
HEALTH_CHECK_INTERVAL=30s

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT USE CASES
# =============================================================================

# Real-time processing (fastest, requires stable WebSocket)
# SCHEDULER_MODE=realtime
# BATCH_SIZE=1
# CONCURRENT_WORKERS=10
# SCHEDULER_ENABLE_POLLING=false

# Reliable processing (balanced speed and reliability)
# SCHEDULER_MODE=hybrid
# BATCH_SIZE=1
# CONCURRENT_WORKERS=5
# SCHEDULER_FALLBACK_TIMEOUT=30s

# Conservative processing (most reliable, slower)
# SCHEDULER_MODE=polling
# BATCH_SIZE=5
# CONCURRENT_WORKERS=3
# SCHEDULER_POLLING_INTERVAL=5s

# =============================================================================
# PROVIDER-SPECIFIC SETTINGS
# =============================================================================

# Infura (free tier: 100,000 requests/day, 10 requests/second)
# ETHEREUM_RATE_LIMIT=100ms
# CONCURRENT_WORKERS=3

# Alchemy (free tier: 300M compute units/month)
# ETHEREUM_RATE_LIMIT=50ms
# CONCURRENT_WORKERS=5

# QuickNode (varies by plan)
# ETHEREUM_RATE_LIMIT=10ms
# CONCURRENT_WORKERS=10
