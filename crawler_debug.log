[Fx] PROVIDE	fx.Lifecycle <= go.uber.org/fx.New.func1()
[Fx] PROVIDE	fx.Shutdowner <= go.uber.org/fx.(*App).shutdowner-fm()
[Fx] PROVIDE	fx.DotGraph <= go.uber.org/fx.(*App).dotGraph-fm()
[Fx] PROVIDE	*config.Config <= ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig()
[Fx] PROVIDE	*config.MongoDBConfig <= main.provideMongoDBConfig()
[Fx] PROVIDE	*config.EthereumConfig <= main.provideEthereumConfig()
[Fx] PROVIDE	*logger.Logger <= ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger()
[Fx] PROVIDE	*database.MongoDB <= ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB()
[Fx] PROVIDE	service.BlockchainService <= fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]])
[Fx] PROVIDE	repository.BlockRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]])
[Fx] PROVIDE	repository.TransactionRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]])
[Fx] PROVIDE	repository.MetricsRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]])
[Fx] PROVIDE	*service.CrawlerService <= ethereum-raw-data-crawler/internal/application/service.NewCrawlerService()
[Fx] INVOKE		main.registerHooks()
[Fx] BEFORE RUN	provide: go.uber.org/fx.New.func1()
[Fx] RUN	provide: go.uber.org/fx.New.func1() in 2.375µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig() in 292.583µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger() in 45.083µs
[Fx] BEFORE RUN	provide: main.provideMongoDBConfig()
[Fx] RUN	provide: main.provideMongoDBConfig() in 1.708µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB() in 1.301056833s
[Fx] BEFORE RUN	provide: main.provideEthereumConfig()
[Fx] RUN	provide: main.provideEthereumConfig() in 10.166µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]]) in 57.084µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]]) in 19.083µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]]) in 13.958µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]]) in 3.917µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewCrawlerService()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewCrawlerService() in 5.792µs
[Fx] HOOK OnStart		main.registerHooks.func1() executing (caller: main.registerHooks)
2025-06-22T17:55:27.603+0700	INFO	crawler/main.go:90	Starting Ethereum Raw Data Crawler	{"version": "1.0.0", "network": "ethereum"}
2025-06-22T17:55:27.917+0700	INFO	service/crawler_service.go:85	Starting crawler service	{"component": "crawler-service"}
2025-06-22T17:55:27.917+0700	INFO	blockchain/ethereum_service.go:42	Connecting to Ethereum node	{"component": "ethereum-service", "rpc_url": "https://mainnet.infura.io/v3/********************************"}
2025-06-22T17:55:28.927+0700	INFO	blockchain/ethereum_service.go:60	Successfully connected to Ethereum node	{"component": "ethereum-service"}
2025-06-22T17:55:29.000+0700	INFO	service/crawler_service.go:196	Starting from configured start block	{"component": "crawler-service", "start_block": "1"}
2025-06-22T17:55:29.000+0700	INFO	service/crawler_service.go:103	Crawler service started successfully	{"component": "crawler-service", "current_block": "1"}
2025-06-22T17:55:29.000+0700	INFO	crawler/main.go:118	Ethereum Raw Data Crawler started successfully
[Fx] HOOK OnStart		main.registerHooks.func1() called by main.registerHooks ran successfully in 1.396964209s
[Fx] RUNNING
2025-06-22T17:55:30.253+0700	DEBUG	service/crawler_service.go:233	Checking blocks for processing	{"component": "crawler-service", "current_block": "1", "latest_block": "22759521"}
2025-06-22T17:55:30.253+0700	INFO	service/crawler_service.go:251	Processing block range	{"component": "crawler-service", "start": "1", "end": "6", "latest": "22759521"}
2025-06-22T17:55:30.253+0700	DEBUG	service/crawler_service.go:320	Processing block	{"component": "crawler-service", "block_number": 2}
2025-06-22T17:55:30.253+0700	DEBUG	blockchain/ethereum_service.go:101	Getting block by number	{"component": "ethereum-service", "block_number": "2"}
2025-06-22T17:55:30.253+0700	DEBUG	service/crawler_service.go:320	Processing block	{"component": "crawler-service", "block_number": 1}
2025-06-22T17:55:30.253+0700	DEBUG	blockchain/ethereum_service.go:101	Getting block by number	{"component": "ethereum-service", "block_number": "1"}
2025-06-22T17:55:30.509+0700	DEBUG	service/crawler_service.go:320	Processing block	{"component": "crawler-service", "block_number": 3}
2025-06-22T17:55:30.509+0700	DEBUG	blockchain/ethereum_service.go:101	Getting block by number	{"component": "ethereum-service", "block_number": "3"}
2025-06-22T17:55:31.091+0700	DEBUG	service/crawler_service.go:320	Processing block	{"component": "crawler-service", "block_number": 4}
2025-06-22T17:55:31.091+0700	DEBUG	blockchain/ethereum_service.go:101	Getting block by number	{"component": "ethereum-service", "block_number": "4"}
