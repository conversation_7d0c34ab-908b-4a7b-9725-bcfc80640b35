[Fx] PROVIDE	fx.Lifecycle <= go.uber.org/fx.New.func1()
[Fx] PROVIDE	fx.Shutdowner <= go.uber.org/fx.(*App).shutdowner-fm()
[Fx] PROVIDE	fx.DotGraph <= go.uber.org/fx.(*App).dotGraph-fm()
[Fx] PROVIDE	*config.Config <= ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig()
[Fx] PROVIDE	*config.MongoDBConfig <= main.provideMongoDBConfig()
[Fx] PROVIDE	*config.EthereumConfig <= main.provideEthereumConfig()
[Fx] PROVIDE	*logger.Logger <= ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger()
[Fx] PROVIDE	*database.MongoDB <= ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB()
[Fx] PROVIDE	service.MessagingService <= fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/messaging.NewNATSMessagingService(), fx.As([[service.MessagingService]])
[Fx] PROVIDE	service.BlockchainService <= fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]])
[Fx] PROVIDE	service.BlockSchedulerService <= fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewWebSocketScheduler(), fx.As([[service.BlockSchedulerService]])
[Fx] PROVIDE	repository.BlockRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]])
[Fx] PROVIDE	repository.TransactionRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]])
[Fx] PROVIDE	repository.MetricsRepository <= fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]])
[Fx] PROVIDE	*service.CrawlerService <= ethereum-raw-data-crawler/internal/application/service.NewCrawlerService()
[Fx] PROVIDE	*service.SchedulerService <= ethereum-raw-data-crawler/internal/application/service.NewSchedulerService()
[Fx] INVOKE		main.registerSchedulerHooks()
[Fx] BEFORE RUN	provide: go.uber.org/fx.New.func1()
[Fx] RUN	provide: go.uber.org/fx.New.func1() in 2.458µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/config.LoadConfig() in 580.875µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/logger.NewLogger() in 43.167µs
[Fx] BEFORE RUN	provide: main.provideMongoDBConfig()
[Fx] RUN	provide: main.provideMongoDBConfig() in 1.75µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/infrastructure/database.NewMongoDB() in 796.767125ms
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/messaging.NewNATSMessagingService(), fx.As([[service.MessagingService]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/messaging.NewNATSMessagingService(), fx.As([[service.MessagingService]]) in 92.833µs
[Fx] BEFORE RUN	provide: main.provideEthereumConfig()
[Fx] RUN	provide: main.provideEthereumConfig() in 3.875µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewEthereumService(), fx.As([[service.BlockchainService]]) in 18.291µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewBlockRepository(), fx.As([[repository.BlockRepository]]) in 7.209µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewTransactionRepository(), fx.As([[repository.TransactionRepository]]) in 3.416µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/adapters/secondary.NewMetricsRepository(), fx.As([[repository.MetricsRepository]]) in 3.916µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewCrawlerService()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewCrawlerService() in 13.75µs
[Fx] BEFORE RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewWebSocketScheduler(), fx.As([[service.BlockSchedulerService]])
[Fx] RUN	provide: fx.Annotate(ethereum-raw-data-crawler/internal/infrastructure/blockchain.NewWebSocketScheduler(), fx.As([[service.BlockSchedulerService]]) in 17.375µs
[Fx] BEFORE RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewSchedulerService()
[Fx] RUN	provide: ethereum-raw-data-crawler/internal/application/service.NewSchedulerService() in 17µs
[Fx] HOOK OnStart		main.registerSchedulerHooks.func1() executing (caller: main.registerSchedulerHooks)
2025-06-23T16:47:54.306+0700	INFO	schedulers/main.go:114	Starting Ethereum Block Scheduler	{"version": "1.0.0", "network": "ethereum", "scheduler_mode": "realtime"}
2025-06-23T16:47:54.821+0700	INFO	messaging/nats_client.go:56	NATS is disabled, skipping connection	{"component": "nats-client"}
2025-06-23T16:47:54.821+0700	INFO	service/crawler_service.go:94	Starting crawler service	{"component": "crawler-service"}
2025-06-23T16:47:54.821+0700	INFO	blockchain/ethereum_service.go:44	Connecting to Ethereum node	{"component": "ethereum-service", "rpc_url": "https://mainnet.infura.io/v3/********************************"}
2025-06-23T16:47:55.836+0700	INFO	blockchain/ethereum_service.go:62	Successfully connected to Ethereum node	{"component": "ethereum-service"}
2025-06-23T16:47:56.058+0700	INFO	service/crawler_service.go:215	Resuming from last processed block	{"component": "crawler-service", "last_block": "22766332", "current_block": "22766333"}
2025-06-23T16:47:56.058+0700	INFO	service/crawler_service.go:112	Crawler started in external scheduler mode	{"component": "crawler-service"}
2025-06-23T16:47:56.058+0700	INFO	service/crawler_service.go:122	Crawler service started successfully	{"component": "crawler-service", "current_block": "22766333"}
2025-06-23T16:47:56.058+0700	INFO	service/scheduler_service.go:85	Starting scheduler service	{"component": "scheduler-service", "mode": "realtime"}
2025-06-23T16:47:56.058+0700	INFO	blockchain/websocket_scheduler.go:51	Starting WebSocket scheduler	{"component": "websocket-scheduler", "ws_url": "wss://eth-mainnet.ws.alchemyapi.io/ws/x6DMAG9Zx4vOWVlS3Dov4"}
